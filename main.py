import pgzrun
import numpy as np
from pygame import Rect
import random
from numba import jit
import time

TITLE = "Noita"
WIDTH = 200
HEIGHT = 200

SHIFT_MATERIAL = 0
SHIFT_DENSITY = 8
MASK_MATERIAL = 0xFF << SHIFT_MATERIAL
MASK_DENSITY = 0xF << SHIFT_DENSITY

def pack_pixel(material_id,material_density):
    """将不同的属性打包成一个32位整数像素"""
    pixel = 0
    pixel |= (material_id & 0xFF) << SHIFT_MATERIAL
    pixel |= (material_density & 0xF) << SHIFT_DENSITY

    return np.uint32(pixel) # 确保是 NumPy 的 32位无符号整数
def unpack_pixel(pixel):
    """从一个32位整数像素中解包属性"""
    material_id = (pixel & MASK_MATERIAL) >> SHIFT_MATERIAL
    material_density = (pixel & MASK_DENSITY) >> SHIFT_DENSITY
    return material_id, material_density




traits = {
    'air': {
        "color": (135, 206, 235),
        'MAT': 0,
        'density': 0.1,
    },
    'sand': {
        "color": (194, 178, 128),
        'MAT': 1,
        'density': 5,
    }
}

# 从 traits 字典中提取常量
MAT_AIR = traits['air']['MAT']
MAT_SAND = traits['sand']['MAT']

default_air = pack_pixel(MAT_AIR, int(traits['air']['density']))

# 从 traits 字典中构建 colors 字典
colors = {
    MAT_AIR: traits['air']['color'],
    MAT_SAND: traits['sand']['color']
}

WORLD_WIDTH = int(WIDTH/5)
WORLD_HEIGHT = int(HEIGHT/5)
world = np.full((WORLD_HEIGHT, WORLD_WIDTH), default_air, dtype=np.uint32)

# FPS 计算变量
fps_counter = 0
fps_timer = time.time()
current_fps = 0

for i in range(100):
    world[random.randint(0, WORLD_HEIGHT-1), random.randint(0, WORLD_WIDTH-1)] = pack_pixel(MAT_SAND, int(traits['sand']['density']))

@jit(nopython=True)  # 如果安装了 numba 可以取消注释
def simulation_step(world):
    # Numba 要求在 JIT 函数内部重新定义这些常量
    SHIFT_MATERIAL = 0
    SHIFT_DENSITY = 8
    MASK_MATERIAL = 0xFF << SHIFT_MATERIAL
    MASK_DENSITY = 0xF << SHIFT_DENSITY

    # 材料常量
    MAT_AIR = 0
    MAT_SAND = 1

    # 密度值
    DENSITY_AIR = 0
    DENSITY_SAND = 5

    # 默认空气像素
    default_air = MAT_AIR | (DENSITY_AIR << SHIFT_DENSITY)

    height, width = world.shape
    # 创建一个临时数组来存储下一帧的状态，避免读写冲突
    next_world = world.copy()

    for y in range(height - 1):
        for x in range(width):
            pixel = world[y, x]

            # 使用位运算在 Numba 中解包
            material_id = (pixel & MASK_MATERIAL) >> SHIFT_MATERIAL
            material_density = (pixel & MASK_DENSITY) >> SHIFT_DENSITY

            if material_id == MAT_SAND:
                # 沙子下落逻辑
                below = world[y+1, x]
                below_mat = (below & MASK_MATERIAL) >> SHIFT_MATERIAL
                below_density = (below & MASK_DENSITY) >> SHIFT_DENSITY

                # 只有当下方密度更小时才能下落
                if material_density > below_density:
                    # 移动沙子
                    next_world[y, x] = default_air
                    next_world[y+1, x] = pixel

    return next_world

def draw():
    screen.clear()
    screen.fill(traits['air']['color'])  # 天空蓝色背景
    for i in range(WORLD_HEIGHT):
        for j in range(WORLD_WIDTH):
            pixel = world[i,j]
            material_id, material_density = unpack_pixel(pixel)
            color = colors[material_id]
            rect = Rect(j*5, i*5, 5, 5)
            screen.draw.filled_rect(rect, color)

    # 显示 FPS
    screen.draw.text(f"FPS: {current_fps:.1f}", (10, 10), color=(255, 255, 255), fontsize=24)

def update():
    global world, fps_counter, fps_timer, current_fps

    # FPS 计算
    fps_counter += 1
    current_time = time.time()
    if current_time - fps_timer >= 1.0:  # 每秒更新一次 FPS
        current_fps = fps_counter / (current_time - fps_timer)
        fps_counter = 0
        fps_timer = current_time

    world = simulation_step(world)
    draw()


pgzrun.go()