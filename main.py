import pgzrun
import numpy as np

TITLE = "Noita"
WIDTH = 800
HEIGHT = 600

SHIFT_MATERIAL = 0
MASK_MATERIAL = 0xFF << SHIFT_MATERIAL


def pack_pixel(material_id):
    """将不同的属性打包成一个32位整数像素"""
    pixel = 0
    # 确保输入值在有效范围内 (例如 material_id 不能超过 255)
    pixel |= (material_id & 0xFF) << SHIFT_MATERIAL
    return np.uint32(pixel) # 确保是 NumPy 的 32位无符号整数
def unpack_pixel(pixel):
    """从一个32位整数像素中解包属性"""
    material_id = (pixel & MASK_MATERIAL) >> SHIFT_MATERIAL
    return material_id




MAT_AIR=0
MAT_SAND=1
default_air = pack_pixel(MAT_AIR)
WORLD_WIDTH=int(WIDTH/5)
WORLD_HEIGHT=int(HEIGHT/5)
world = np.full((WORLD_HEIGHT, WORLD_WIDTH), default_air, dtype=np.uint32)
pack_pixel(MAT_SAND)
world[10,10] = pack_pixel(MAT_SAND)
colors = {MAT_AIR: (255, 255, 255), MAT_SAND: (255, 255, 0)}
def draw():
    screen.clear()
    screen.fill((255, 255, 255))
    for i in range(WORLD_HEIGHT):
        for j in range(WORLD_WIDTH):
            pixel = world[i,j]
            material_id = unpack_pixel(pixel)
            color=colors[material_id]
            screen.draw.rect((j*5, i*5),( 5, 5), color)

def update():
    draw()


pgzrun.go()