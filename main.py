import pgzrun
import numpy as np
from pygame import Rect
from numba import jit
import time

TITLE = "Noita"
WIDTH = 300
HEIGHT = 300

SHIFT_MATERIAL = 0
SHIFT_DENSITY = 8
SHIFT_MOVE_TENDENCY = 12
MASK_MATERIAL = 0xFF << SHIFT_MATERIAL
MASK_DENSITY = 0xF << SHIFT_DENSITY
MASK_MOVE_TENDENCY = 0xF << SHIFT_MOVE_TENDENCY

def pack_pixel(material_id, material_density, move_tendency=0):
    """将不同的属性打包成一个32位整数像素"""
    pixel = 0
    pixel |= (material_id & 0xFF) << SHIFT_MATERIAL
    pixel |= (material_density & 0xF) << SHIFT_DENSITY
    pixel |= (move_tendency & 0xF) << SHIFT_MOVE_TENDENCY

    return np.uint32(pixel) # 确保是 NumPy 的 32位无符号整数

def unpack_pixel(pixel):
    """从一个32位整数像素中解包属性"""
    material_id = (pixel & MASK_MATERIAL) >> SHIFT_MATERIAL
    material_density = (pixel & MASK_DENSITY) >> SHIFT_DENSITY
    move_tendency = (pixel & MASK_MOVE_TENDENCY) >> SHIFT_MOVE_TENDENCY
    return material_id, material_density, move_tendency




traits = {
    'air': {
        "color": (135, 206, 235),
        'MAT': 0,
        'density': 0.1,
        'move_tendency': 0,  # 无移动倾向
    },
    'sand': {
        "color": (194, 178, 128),
        'MAT': 1,
        'density': 5,
        'move_tendency': 2,  # 向下移动倾向
    },
    'stone': {
        "color": (0, 0, 0),
        'MAT': 2,
        'density': 10,
        'move_tendency': 2,  # 向下移动倾向
    }
}

# 从 traits 字典中提取常量
MAT_AIR = traits['air']['MAT']
MAT_SAND = traits['sand']['MAT']
MAT_STONE = traits['stone']['MAT']

default_air = pack_pixel(MAT_AIR, int(traits['air']['density']), traits['air']['move_tendency'])

# 从 traits 字典中构建 colors 字典
colors = {
    MAT_AIR: traits['air']['color'],
    MAT_SAND: traits['sand']['color'],
    MAT_STONE: traits['stone']['color']
}

WORLD_WIDTH = int(WIDTH/5)
WORLD_HEIGHT = int(HEIGHT/5)
world = np.full((WORLD_HEIGHT, WORLD_WIDTH), default_air, dtype=np.uint32)

# FPS 计算变量
fps_counter = 0
fps_timer = time.time()
current_fps = 0

# 性能优化：减少物理模拟频率
simulation_counter = 0
SIMULATION_SKIP = 2  # 每3帧执行一次物理模拟 (跳过2帧)

# 优化的随机初始化：批量生成随机坐标，避免重复计算
def initialize_materials():
    """初始化世界中的材料"""
    # 预计算像素值，避免重复调用 pack_pixel
    sand_pixel = pack_pixel(MAT_SAND, int(traits['sand']['density']), traits['sand']['move_tendency'])
    stone_pixel = pack_pixel(MAT_STONE, int(traits['stone']['density']), traits['stone']['move_tendency'])

    # 批量生成随机坐标
    sand_positions = np.random.randint(0, [WORLD_HEIGHT, WORLD_WIDTH], size=(100, 2))
    stone_positions = np.random.randint(0, [WORLD_HEIGHT, WORLD_WIDTH], size=(50, 2))

    # 批量设置沙子
    for y, x in sand_positions:
        world[y, x] = sand_pixel

    # 批量设置石头
    for y, x in stone_positions:
        world[y, x] = stone_pixel

# 执行初始化
initialize_materials()

@jit(nopython=True)  # 如果安装了 numba 可以取消注释
def simulation_step(world):
    # Numba 要求在 JIT 函数内部重新定义这些常量
    SHIFT_MATERIAL = 0
    SHIFT_DENSITY = 8
    SHIFT_MOVE_TENDENCY = 12
    MASK_MATERIAL = 0xFF << SHIFT_MATERIAL
    MASK_DENSITY = 0xF << SHIFT_DENSITY
    MASK_MOVE_TENDENCY = 0xF << SHIFT_MOVE_TENDENCY

    # 材料常量
    MAT_AIR = 0
    MAT_SAND = 1

    # 移动倾向常量
    MOVE_NONE = 0
    MOVE_UP = 1
    MOVE_DOWN = 2
    MOVE_LEFT = 3
    MOVE_RIGHT = 4
    MOVE_SPECIFIC = 5

    height, width = world.shape
    # 创建一个临时数组来存储下一帧的状态，避免读写冲突
    next_world = world.copy()

    for y in range(height - 1):
        for x in range(width):
            pixel = world[y, x]

            # 使用位运算在 Numba 中解包
            material_id = (pixel & MASK_MATERIAL) >> SHIFT_MATERIAL
            material_density = (pixel & MASK_DENSITY) >> SHIFT_DENSITY
            move_tendency = (pixel & MASK_MOVE_TENDENCY) >> SHIFT_MOVE_TENDENCY

            # 根据移动倾向进行移动判断
            if move_tendency == MOVE_DOWN and y < height - 1:
                # 向下移动倾向
                below = world[y+1, x]
                below_mat = (below & MASK_MATERIAL) >> SHIFT_MATERIAL
                below_density = (below & MASK_DENSITY) >> SHIFT_DENSITY

                # 只有当下方密度更小时才能下落
                if material_density > below_density:
                    # 交换两个像素的位置
                    next_world[y, x] = below
                    next_world[y+1, x] = pixel

    return next_world

def draw():
    screen.clear()
    screen.fill(traits['air']['color'])  # 天空蓝色背景

    # 预创建 Rect 对象以减少内存分配
    pixel_rect = Rect(0, 0, 5, 5)

    for i in range(WORLD_HEIGHT):
        for j in range(WORLD_WIDTH):
            pixel = world[i,j]
            material_id, material_density, move_tendency = unpack_pixel(pixel)

            # 只绘制非空气像素，减少绘制调用
            if material_id != 0:  # 0 是 MAT_AIR
                color = colors[material_id]
                pixel_rect.x = j * 5
                pixel_rect.y = i * 5
                screen.draw.filled_rect(pixel_rect, color)

    # 显示 FPS
    screen.draw.text(f"FPS: {current_fps:.1f}", (10, 10), color=(255, 255, 255), fontsize=24)

def update():
    global world, fps_counter, fps_timer, current_fps

    # FPS 计算
    fps_counter += 1
    current_time = time.time()
    if current_time - fps_timer >= 1.0:  # 每秒更新一次 FPS
        current_fps = fps_counter / (current_time - fps_timer)
        fps_counter = 0
        fps_timer = current_time

    world = simulation_step(world)
    draw()


pgzrun.go()